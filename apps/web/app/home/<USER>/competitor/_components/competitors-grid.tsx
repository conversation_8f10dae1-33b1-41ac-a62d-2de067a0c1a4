'use client';

import { useState, useEffect, useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ModuleRegistry } from 'ag-grid-community';
import {
  ClientSideRowModelModule,
  PaginationModule,
  ValidationModule,
  TextFilterModule,
  NumberFilterModule,
  DateFilterModule
} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-theme-quartz.css';

// Register AG Grid modules
ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  PaginationModule,
  ValidationModule,
  TextFilterModule,
  NumberFilterModule,
  DateFilterModule
]);

interface Competitor {
  id: string;
  app_id: string;
  app_name: string;
  short_description: string;
  languages: string[];
  highlights: string;
  category: string;
  rating: number;
  review_count: number;
  has_trial: boolean;
  is_free: boolean;
  trial_days: number;
  categories: string[];
}

export function CompetitorsGrid() {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompetitors = async () => {
      try {
        const response = await fetch('/api/competitors');
        if (!response.ok) throw new Error('Failed to fetch competitors');
        const data = await response.json();
        console.log('Table View Data:', data);
        setCompetitors(data);
      } catch (error) {
        console.error('Error fetching competitors:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCompetitors();
  }, []);

  const columnDefs = useMemo(() => [
    { 
      field: 'app_name', 
      headerName: 'App Name', 
      sortable: true, 
      filter: true 
    },
    { 
      field: 'app_id', 
      headerName: 'App ID', 
      sortable: true 
    },
    { 
      field: 'short_description', 
      headerName: 'Short Description', 
      flex: 2 
    },
    { 
      field: 'languages', 
      headerName: 'Languages',
      valueFormatter: (params: any) => params.value?.join(', ') || 'N/A'
    },
    { 
      field: 'highlights', 
      headerName: 'Highlights', 
      flex: 2 
    },

    { 
      field: 'rating', 
      headerName: 'Rating', 
      sortable: true, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      field: 'review_count', 
      headerName: 'Review Count', 
      sortable: true, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      field: 'has_trial', 
      headerName: 'Has Trial', 
      cellRenderer: (params: any) => params.value ? '✅ Yes' : '❌ No' 
    },
    { 
      field: 'is_free', 
      headerName: 'Is Free', 
      cellRenderer: (params: any) => params.value ? '✅ Yes' : '❌ No' 
    },
    { 
      field: 'trial_days', 
      headerName: 'Trial Days', 
      sortable: true 
    },
    { 
      field: 'categories',
      headerName: 'Categories',
      cellRenderer: (params: any) =>
        params.value && params.value.length > 0
          ? (
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                {params.value.map((cat: string) => (
                  <span
                    key={cat}
                    style={{
                      background: '#dbeafe',
                      color: '#1e40af',
                      padding: '2px 8px',
                      borderRadius: '9999px',
                      fontSize: '12px',
                      marginRight: '4px',
                      marginBottom: '2px',
                      display: 'inline-block'
                    }}
                  >
                    {cat}
                  </span>
                ))}
              </div>
            )
          : <span style={{ color: '#9ca3af', fontSize: '12px' }}>N/A</span>,
      flex: 2,
    },
  ], []);

  const defaultColDef = useMemo(() => ({
    flex: 1,
    minWidth: 100,
    resizable: true,
  }), []);

  if (loading) return <div className="p-4">Loading competitors data...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;
  if (!competitors.length) return <div className="p-4">No competitors found</div>;

  return (
    <div className="ag-theme-quartz h-[500px] w-full">
      <AgGridReact
        rowData={competitors}
        columnDefs={columnDefs as any}
        defaultColDef={defaultColDef}
        pagination={true}
        paginationPageSize={10}
      />
    </div>
  );
}


