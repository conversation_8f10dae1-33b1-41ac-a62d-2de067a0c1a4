import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Heading } from '@kit/ui/heading';

import  ActivitySummary  from './home-activity-summary';
// import  CompetitorActivity  from './competitor-activity';
import { DashboardPageHeader } from './dashboard-header';
import { loadUserWorkspace } from '../_lib/server/load-user-workspace';


import { use } from 'react';

export default function Dashboard()
{  const workspace = use(loadUserWorkspace());
    return(
<>
        <DashboardPageHeader workspace={workspace}/>
        <PageBody>
        
         <ActivitySummary/>
        </PageBody>
        

</>
    );
}