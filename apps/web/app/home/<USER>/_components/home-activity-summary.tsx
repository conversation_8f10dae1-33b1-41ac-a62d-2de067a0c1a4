"use client";
import React, { useEffect, useState, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { ArrowDown, ArrowUp, Menu, TrendingUp, BarChart, FileText, DollarSign, Bell, Star, LayoutGrid, ExternalLink } from 'lucide-react';
import { AgGridReact } from 'ag-grid-react';
import { AgCharts } from 'ag-charts-react';
import type { AgPolarChartOptions, AgCartesianChartOptions } from 'ag-charts-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { ModuleRegistry } from 'ag-grid-community';
import { ClientSideRowModelModule } from 'ag-grid-community';

ModuleRegistry.registerModules([ClientSideRowModelModule]);

interface ChartDatum {
  name: string;
  value?: number;
  price?: number;
  rating?: number;
  [key: string]: any;
}

export default function ActivitySummary() {
  const gridRef = useRef<any>(null);
  const [competitors, setCompetitors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCompetitors() {
      try {
        setLoading(true);
        // Use the helper to fetch competitors with pricing plans
        const res = await fetch('/api/competitors');
        const data = await res.json();
        setCompetitors(data);
      } catch (err: any) {
        setError(err.message || 'Unknown error');
      } finally {
        setLoading(false);
      }
    }
    fetchCompetitors();
  }, []);

  // Helper function to get app display name
  const getAppName = (app: any) => {
    return app.name || app.app_name || app.title || `App ${app.app_id || 'Unknown'}`;
  };

  // Helper function to get app URL
  const getAppUrl = (app: any) => {
    return app.url || app.app_url || app.store_url || '#';
  };

  // Helper function to render app name with link
  const renderAppName = (app: any) => {
    const name = getAppName(app);
    const url = getAppUrl(app);
    return (
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center hover:underline"
      >
        {name}
        <ExternalLink className="ml-1 h-4 w-4" />
      </a>
    );
  };

  // Helper function to get monthly price
  const getMonthlyPrice = (app: any) => {
    return Number(app.pricing_plan?.monthly_price)
      || Number(app.monthly_price)
      || Number(app.price)
      || Number(app.monthly)
      || 0;
  };

  if (loading) {
    return null;
  }
 
  if (error) {
    return <div className="text-red-500 p-4 border border-red-200 rounded-lg">Error: {error}</div>;
  }
 
  if (!competitors || competitors.length === 0) {
    return <div className="text-gray-500 p-4 border border-gray-200 rounded-lg">No competitor data available.</div>;
  }

  // All calculations use only the competitors array
  const avgRating =
    competitors.reduce((sum, app) => sum + (Number(app.rating) || 0), 0) / competitors.length;
 
  const avgReviews =
    competitors.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0) / competitors.length;
 
  const topRated = competitors.reduce((prev, curr) =>
    (Number(curr.rating) || 0) > (Number(prev.rating) || 0) ? curr : prev
  );
 
  const mostReviewed = competitors.reduce((prev, curr) =>
    (Number(curr.review_count) || 0) > (Number(prev.review_count) || 0) ? curr : prev
  );
 
  const totalReviews = competitors.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0);
 
  const marketShares = competitors.map(app => ({
    name: getAppName(app),
    share: totalReviews > 0 ? (((Number(app.review_count) || 0) / totalReviews) * 100).toFixed(1) : '0.0',
    app: app
  }));

  // Get all monthly prices
  const competitorPrices = competitors.map((app: any) => {
    const monthlyPrice = getMonthlyPrice(app);
    return { app, price: monthlyPrice };
  }).filter((entry: any) => entry.price > 0);

  const prices = competitorPrices.map((entry: any) => entry.price);
  const lowestPrice = prices.length > 0 ? Math.min(...prices) : 'N/A';
  const highestPrice = prices.length > 0 ? Math.max(...prices) : 'N/A';
  const avgPrice = prices.length > 0 ? prices.reduce((a: number, b: number) => a + b, 0) / prices.length : 0;
  const sortedPrices = [...prices].sort((a: number, b: number) => a - b);
 
  const priceRanks = competitorPrices.map((entry: any) => {
    return {
      name: getAppName(entry.app),
      price: entry.price,
      rank: entry.price > 0 ? sortedPrices.indexOf(entry.price) + 1 : 'N/A',
      displayPrice: entry.price > 0 ? `$${entry.price}/month` : 'Free',
      app: entry.app
    };
  }).sort((a: any, b: any) => a.price - b.price);

  const cheapestApp = competitorPrices.find((entry: any) => entry.price === lowestPrice)?.app;
  const mostExpensiveApp = competitorPrices.find((entry: any) => entry.price === highestPrice)?.app;

  // Find all apps with the highest rating
  const maxRating = Math.max(...competitors.map(app => Number(app.rating) || 0));
  const topRatedApps = competitors.filter(app => Number(app.rating) === maxRating);

  // Prepare data for AG Grid
  const agGridRowData = competitors.map(app => ({
    ...app,
    name: getAppName(app),
    monthly_price: getMonthlyPrice(app),
    categories: app.categories || [],
  }));

  // AG Grid column definitions
  const agGridColumns = [
    {
      headerName: 'App Name',
      field: 'name',
      cellRenderer: (params: any) => renderAppName(params.data),
      flex: 2,
    },
    {
      headerName: 'Categories',
      field: 'categories',
      cellRenderer: (params: any) =>
        params.value && params.value.length > 0 ? (
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {params.value.map((cat: string) => (
              <span
                key={cat}
                style={{
                  background: '#f3f4f6',
                  color: '#374151',
                  padding: '2px 8px',
                  borderRadius: '9999px',
                  fontSize: '12px',
                  marginRight: '4px',
                  marginBottom: '2px',
                  display: 'inline-block',
                  border: '1px solid #e5e7eb',
                }}
              >
                {cat}
              </span>
            ))}
          </div>
        ) : (
          <span style={{ color: '#9ca3af', fontSize: '12px' }}>N/A</span>
        ),
      flex: 2,
    },
    {
      headerName: 'Rating',
      field: 'rating',
      valueFormatter: (params: any) => Number(params.value || 0).toFixed(2),
      flex: 1,
    },
    {
      headerName: 'Review Count',
      field: 'review_count',
      valueFormatter: (params: any) => Number(params.value || 0).toLocaleString(),
      flex: 1,
    },
    {
      headerName: 'Monthly Price',
      field: 'monthly_price',
      valueFormatter: (params: any) => params.value ? `$${Number(params.value).toFixed(2)}` : 'Free',
      flex: 1,
    },
  ];

  // Prepare data for AG Charts
  const marketShareChartData = marketShares
    .sort((a, b) => parseFloat(b.share) - parseFloat(a.share))
    .map(ms => ({
      name: ms.name,
      value: parseFloat(ms.share),
      app: ms.app
    }));

  const priceChartData = priceRanks.map(pr => ({
    name: pr.name,
    price: pr.price,
    app: pr.app
  }));

  const ratingChartData = competitors.map(app => ({
    name: getAppName(app),
    rating: Number(app.rating) || 0,
    app: app
  })).sort((a, b) => b.rating - a.rating);

  // Market Share Pie Chart fills/colors
  const marketShareFills = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

  // Market Share Pie Chart
  const marketShareChartOptions: AgPolarChartOptions = {
    width: 600,
    height: 600,
    data: marketShareChartData,
    series: [{
      type: 'pie',
      angleKey: 'value',
      legendItemKey: 'name',
      fills: marketShareFills,
      strokeWidth: 0,
      calloutLabelKey: 'name',
      calloutLabel: {
        enabled: true,
        formatter: (params: { datum: ChartDatum }) => `${params.datum.name}: ${params.datum.value?.toFixed(1)}%`,
      },
    }],
    title: {
      text: 'Market Share by Reviews',
    },
    legend: {
      enabled: false,
    },
  };

  // Price Distribution Bar Chart
  const priceChartOptions: AgCartesianChartOptions = {
    width: 600,
    height: 600,
    data: priceChartData,
    series: [{
      type: 'bar',
      xKey: 'name',
      yKey: 'price',
      fill: '#8884d8',
    }],
    axes: [
      { type: 'category', position: 'bottom', title: { text: 'Apps' }, label: { rotation: -45 } },
      { type: 'number', position: 'left', title: { text: 'Price ($/month)' } },
    ],
    title: { text: 'Price Distribution' },
    legend: { enabled: false },
  };

  // Rating Distribution Bar Chart
  const ratingChartOptions: AgCartesianChartOptions = {
    width: 600,
    height: 600,
    data: ratingChartData,
    series: [{
      type: 'bar',
      xKey: 'name',
      yKey: 'rating',
      fill: '#82ca9d',
    }],
    axes: [
      { type: 'category', position: 'bottom', title: { text: 'Apps' }, label: { rotation: -45 } },
      { type: 'number', position: 'left', title: { text: 'Rating (0-5)' }, min: 0, max: 5 },
    ],
    title: { text: 'Rating Distribution' },
    legend: { enabled: false },
  };

  // Calculate unique categories
  const allCategories = competitors.flatMap(app => app.categories || []);
  const uniqueCategories = Array.from(new Set(allCategories));

  // Mock/placeholder for market leaders and avg category growth
  const marketLeadersCount = topRatedApps.length; // or use another logic if you have it
  const avgCategoryGrowth = '+3.2% vs last month'; // Placeholder

  return (
    <div className={'flex flex-col space-y-4 pb-16 duration-500 animate-in fade-in'}>
      <div className={'grid grid-cols-1 md:grid-cols-2 gap-4'}>

        {/* Top Rated Competitor(s) */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Top Rated Competitor{topRatedApps.length > 1 ? 's' : ''}</p>
              <h2 className="text-2xl font-bold flex flex-wrap gap-2">
                {topRatedApps.map((app, idx) => (
                  <span key={getAppName(app)}>{renderAppName(app)}{idx < topRatedApps.length - 1 ? ',' : ''}</span>
                ))}
              </h2>
              <p className="text-sm text-gray-700">Rating: {maxRating.toFixed(1)} ⭐</p>
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <Star className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Competitor with Most Reviews */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Most Reviewed Competitor</p>
              <h3 className="text-2xl font-bold">{renderAppName(mostReviewed)}</h3>
              <p className="text-sm text-gray-700">Reviews: {Number(mostReviewed.review_count || 0).toLocaleString()}</p>
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <FileText className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Average Rating & Reviews */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Market Averages</p>
              <h3 className="text-2xl font-bold">{avgRating.toFixed(2)} ⭐</h3>
              <p className="text-sm text-gray-700">Avg Reviews: {avgReviews.toFixed(0)}</p>
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <BarChart className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Total Market Data */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Market Data</p>
              <h2>{loading ? '...' : competitors.length}</h2>
              <p className="text-sm text-gray-700">Competitors tracked</p>
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <LayoutGrid className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

      </div>

      {/* Pricing Intelligence Cards */}
      <div className={'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6'}>
       
        {/* Lowest Competitor Price */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Lowest Monthly Price</p>
              <h3 className="text-2xl font-bold">
                {typeof lowestPrice === 'number' ? `$${lowestPrice}/month` : lowestPrice}
              </h3>
              {cheapestApp && (
                <p className="text-sm text-gray-700">
                  {renderAppName(cheapestApp)}
                </p>
              )}
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <DollarSign className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Highest Competitor Price */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Highest Monthly Price</p>
              <h3 className="text-2xl font-bold">
                {typeof highestPrice === 'number' ? `$${highestPrice}/month` : highestPrice}
              </h3>
              {mostExpensiveApp && (
                <p className="text-sm text-gray-700">
                  {renderAppName(mostExpensiveApp)}
                </p>
              )}
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <DollarSign className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Average Market Price */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Average Monthly Price</p>
              <h3 className="text-2xl font-bold">
                {avgPrice > 0 ? `$${avgPrice.toFixed(2)}/month` : 'N/A'}
              </h3>
              <p className="text-sm text-gray-700">
                {prices.length} paid apps
              </p>
            </div>
            <div className="rounded-full border border-gray-200 p-3">
              <BarChart className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

      </div>

      {/* Detailed Analysis Cards */}
      <div className={'grid grid-cols-1 lg:grid-cols-2 gap-4 mt-6'}>
       
        {/* Price Ranking */}
        <Card className="p-6 border border-gray-200">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-500 mb-3">Monthly Price Ranking (1 = cheapest)</p>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {priceRanks.map((pr, index) => (
                  <div key={pr.name} className="flex justify-between items-center text-sm">
                    <span className="font-medium text-gray-800">
                      {renderAppName(pr.app)}:
                    </span>
                    <span className="text-gray-700">
                      {pr.displayPrice} {pr.rank !== 'N/A' && `(#${pr.rank})`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div className="rounded-full border border-gray-200 p-3 ml-4">
              <DollarSign className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>

        {/* Market Share by Review Volume */}
        <Card className="p-6 border border-gray-200 flex items-center justify-center w-full max-w-full">
          <div className="flex flex-row w-full items-center justify-center gap-4">
            {/* Pie Chart (smaller) */}
            <div className="h-[300px] w-[300px] flex items-center justify-center">
              {/* @ts-ignore: AG Charts type mismatch for label property */}
              <AgCharts options={{ ...marketShareChartOptions, width: 300, height: 300 }} />
            </div>
            {/* App List Legend */}
            <div className="flex flex-col gap-1">
              {marketShares
                .sort((a, b) => parseFloat(b.share) - parseFloat(a.share))
                .map((ms, idx) => (
                  <div key={ms.name} className="flex items-center gap-1 text-xs font-normal">
                    {/* Color dot matching pie chart color */}
                    <span
                      className="inline-block rounded-full"
                      style={{ width: 12, height: 12, backgroundColor: marketShareFills[idx % marketShareFills.length] }}
                    />
                    <span>{renderAppName(ms.app)}</span>
                    <span className="ml-1 text-gray-500 text-xs">{ms.share}%</span>
                  </div>
                ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Price Distribution Insights */}
      {prices.length > 0 && (
        <Card className="p-6 border border-gray-200 mt-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-500 mb-3">Monthly Pricing Insights</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Free Apps:</span>
                  <p className="text-gray-600">{competitors.length - prices.length}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Paid Apps:</span>
                  <p className="text-gray-600">{prices.length}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Price Range:</span>
                  <p className="text-gray-600">
                    ${Math.min(...prices)}/month - ${Math.max(...prices)}/month
                  </p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Median Price:</span>
                  <p className="text-gray-600">
                    ${prices.sort((a, b) => a - b)[Math.floor(prices.length / 2)]}/month
                  </p>
                </div>
              </div>
            </div>
            <div className="rounded-full border border-gray-200 p-3 ml-4">
              <BarChart className="text-gray-600" size={24} />
            </div>
          </div>
        </Card>
      )}

      {/* Hidden AG Grid for data management */}
      <div style={{ position: 'absolute', left: '-9999px', width: 0, height: 0, overflow: 'hidden' }}>
        <div className="ag-theme-alpine" style={{ width: 600, height: 400 }}>
          <AgGridReact
            ref={gridRef}
            rowData={agGridRowData}
            columnDefs={agGridColumns}
            domLayout="autoHeight"
            enableCharts={true}
            defaultColDef={{ resizable: true, sortable: true, filter: true }}
          />
        </div>
      </div>
    </div>
  );
}