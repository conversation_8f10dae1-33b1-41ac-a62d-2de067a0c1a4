export interface Competitor {
  id: string;
  app_id: number;
  user_id: string;
  created_at: string;
  app_name: string;
  app_url: string;
  updated_at: string;
}

export interface App {
  app_id: number;
  app_name: string;
  app_slug: string;
  url: string;
  video_url: string;
  short_description: string;
  long_description: string;
  languages: string[];
  works_with: string[];
  review_count: number;
  rating: number;
  highlights: string[];
  developer: string;
  developer_url: string;
  built_for_shopify: boolean;
}

export interface PricingPlan {
  app_id: number;
  plan_id: number;
  plan_name: string;
  billing_frequency: string;
  features: string[];
  monthly_price: number;
  yearly_price: number;
  savings_percentage: number;
  additional_charges: number;
  trial_days_value: number;
  trial_days_type: string;  
  has_trial: boolean;
  is_free: boolean;
}   

export interface Screenshot {
  app_id: number;
  screenshot_id: number;
  image_type: string;
  screenshot_url: string;
  height: number;     
  width: number;
} 


