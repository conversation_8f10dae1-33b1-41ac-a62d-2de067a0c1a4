import { supabase } from '../lib/supabase/client';
import { getSupabaseRouteHandlerClient } from '@kit/supabase/route-handler-client';
import { cookies } from 'next/headers';
import { App, Competitor, PricingPlan, Screenshot } from '../app/types/data';

export async function fetchCompetitors(user_id?: string): Promise<Competitor[] | null> {
  const query = supabase
    .from('competitors')
    .select(`
      id,
      app_id,
      user_id,
      created_at,
      app_name,
      app_url,
      updated_at
    `);

  if (user_id) {
    query.eq('user_id', user_id);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Supabase fetch error:', error.message);
    return null;
  }

  if (!data) {
    return null;
  }

  return data as unknown as Competitor[];
}

export async function fetchApps(appIds?: number[]): Promise<(App & { categories: string[] })[] | null> {
  const query = supabase
    .from('shopify_apps')
    .select(`
      app_id,
      app_name,
      app_slug,
      url,
      video_url,
      short_description,
      long_description,
      languages,
      works_with,
      review_count,
      rating,
      highlights,
      developer,
      developer_url,
      built_for_shopify,
      category_features
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    console.error({ error }, 'Failed to fetch shopify apps data');
    return null;
  }

  if (!data || data.length === 0) {
    console.info('No shopify apps found in Supabase');
    return null;
  }

  const appsWithCategories = data.map(({ category_features, ...app }) => {
    let categories: string[] = [];

    if (typeof category_features === 'string') {
      try {
        const parsed = JSON.parse(category_features);
        if (Array.isArray(parsed)) {
          categories = parsed.map(f => f?.heading).filter(Boolean);
        }
      } catch (err) {
        console.warn(`Failed to parse category_features for app ${app.app_name}`);
      }
    } else if (Array.isArray(category_features)) {
      categories = category_features.map(f => f?.heading).filter(Boolean);
    }

    const uniqueCategories = Array.from(new Set(categories));

    return {
      ...app,
      categories: uniqueCategories,
    };
  });
  return appsWithCategories;
}


export async function fetchPricingPlans(appIds?: number[]): Promise<PricingPlan[] | null> {
  const query = supabase
    .from('pricing_plans')
    .select(`
      app_id,
      plan_id,
      plan_name,
      billing_frequency,
      features,
      monthly_price,
      yearly_price,
      savings_percentage,
      additional_charges,
      trial_days_value,
      trial_days_type,
      has_trial,
      is_free
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    console.error({ error: error }, 'Failed to fetch pricing plans data');
    return null;
  }

  if (!data || data.length === 0) {
    console.info('No pricing plans found in Supabase');
    return null;
  }

  return data as PricingPlan[];
}

export async function fetchScreenshots(appIds?: number[]): Promise<Screenshot[] | null> {
  const query = supabase
    .from('screenshots')
    .select(`
      app_id,  
      screenshot_id,
      image_type,
      screenshot_url,
      height,
      width
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    console.error({ error: error }, 'Failed to fetch screenshots data');
    return null;
  }

  if (!data || data.length === 0) {
    console.info('No screenshots found in Supabase');
    return null;
  }

  return data as Screenshot[];
}

export async function fetchAppsByCategoryFeature(categoryFeatureHeading: string = 'SEO'): Promise<(App & { categories: string[] })[] | null> {
  // Calling a PostgreSQL function (RPC) to handle complex JSON array querying.
  // This method directly uses the working SQL query you provided, encapsulated in a database function.
  const { data, error } = await supabase.rpc('get_apps_by_category_feature', { target_heading: categoryFeatureHeading });

  if (error) {
    console.error({ error }, `Failed to fetch shopify apps by category feature using RPC: ${categoryFeatureHeading}`);
    return null;
  }

  if (!data || data.length === 0) {
    console.info(`No shopify apps found for category feature: ${categoryFeatureHeading}`);
    return null;
  }

  // The RPC returns data directly from the shopify_apps table, so we need to re-extract categories
  // as your App type expects them as a separate 'categories' array.
  const appsWithCategories = data.map((app: any) => {
    let categories: string[] = [];
    // Ensure category_features is treated as string for parsing if it's JSON type
    const categoryFeatures = typeof app.category_features === 'string' ? JSON.parse(app.category_features) : app.category_features;

    if (Array.isArray(categoryFeatures)) {
      categories = categoryFeatures.map((f: any) => f?.heading).filter(Boolean);
    }

    const uniqueCategories = Array.from(new Set(categories));

    return {
      ...app,
      categories: uniqueCategories,
    };
  });
  return appsWithCategories;
}

export async function getAppDetailsByUrl(appUrl: string): Promise<App | null> {
  const { data, error } = await supabase
    .from('shopify_apps')
    .select('*')
    .eq('url', appUrl)
    .single();

  if (error) {
    console.error('Error fetching app details:', error);
    return null;
  }

  return data as App;
}

export async function addCompetitor(appUrl: string, userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // First get the app details from shopify_apps table
    const appDetails = await getAppDetailsByUrl(appUrl);
    
    if (!appDetails) {
      return { success: false, error: 'App not found in Shopify App Store' };
    }

    const competitorData = {
      app_id: appDetails.app_id,
      user_id: userId,
      app_name: appDetails.app_name,
      app_url: appUrl
    };

    const { error } = await supabase
      .from('competitors')
      .insert([competitorData]);

    if (error) {
      console.error('Error adding competitor:', error);
      return { success: false, error: error.message };
    }
   
    return { success: true };
  } catch (error) {
    console.error('Failed to add competitor:', error);
    return { success: false, error: 'Failed to add competitor' };
  }
}

export async function deleteCompetitor(id: string, userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // First verify the competitor exists and belongs to the user
    const { data: existingCompetitors, error: fetchError } = await supabase
      .from('competitors')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId);

    if (fetchError) {
      console.error('Error fetching competitor:', fetchError);
      return { success: false, error: 'Failed to verify competitor' };
    }

    if (!existingCompetitors || existingCompetitors.length === 0) {
      console.error('Competitor not found or does not belong to user');
      return { success: false, error: 'Competitor not found' };
    }

    // Perform the deletion
    const { error: deleteError } = await supabase
      .from('competitors')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting competitor:', deleteError);
      return { success: false, error: deleteError.message };
    }
   
    return { success: true };
  } catch (error) {
    console.error('Failed to delete competitor:', error);
    return { success: false, error: 'Failed to delete competitor' };
  }
}

export async function getCurrentUser() {
  const supabase = getSupabaseRouteHandlerClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }
  return user;
}

export async function fetchCompetitorsWithPricingPlans() {
  const { data, error } = await supabase
    .from('shopify_apps')
    .select(`
      app_id,
      app_name,
      rating,
      review_count,
      pricing_plans (
        plan_id,
        plan_name,
        monthly_price,
        yearly_price,
        is_free,
        has_trial
      )
    `);

  if (error) throw new Error(error.message);
  return data;
}

export async function fetchHistoricalCompetitors(user_id: string, app_ids: (string | number)[]): Promise<Pick<App, 'app_id' | 'review_count' | 'rating'>[] | null> {
  console.log('Fetching historical competitor data', { user_id, app_ids });

  // **IMPORTANT:** Replace 'competitor_historical_data' with the actual name of your historical data table.
  const { data, error } = await supabase
    .from('competitor_shopify_apps') // <-- Using competitor_shopify_apps as per user
    .select('app_id, review_count, rating, url') // Also selecting URL for potential verification
    .in('app_id', app_ids.map(String)) // Ensure app_ids are strings if your table uses text/varchar for app_id
    // For this comparison approach, we might not need complex date filtering here
    // .gte('created_at', sevenDaysAgo.toISOString()) // Filter for records in the last 7 days
    .order('created_at', { ascending: false }); // Get most recent first if multiple exist

  if (error) {
    console.error('Error fetching historical competitor data from competitor_shopify_apps:', error);
    return null;
  }

  // Simple approach: just return the fetched data. The comparison logic will be in the API route.
  return data as Pick<App, 'app_id' | 'review_count' | 'rating'>[] | null;
}

// New function to fetch review counts and ratings from both tables for comparison using app_url
export async function fetchReviewCountsAndRatingsForComparison(appUrl: string): Promise<{
  currentReviewCount: number | null;
  competitorReviewCount: number | null;
  currentRating: number | null;
  competitorRating: number | null;
} | null> {
  console.log('Fetching review counts and ratings for comparison:', { appUrl });

  try {
    // Fetch current review count and rating from shopify_apps using app_url
    const { data: currentAppData, error: currentError } = await supabase
      .from('shopify_apps')
      .select('review_count, rating')
      .eq('url', appUrl)
      .single();

    if (currentError) {
      console.error('Error fetching current review count and rating from shopify_apps:', currentError);
      // Continue to fetch from competitor_shopify_apps even if current fetch fails
    }

    // Fetch competitor review count and rating from competitor_shopify_apps using app_url
    const { data: competitorAppData, error: competitorError } = await supabase
      .from('competitor_shopify_apps')
      .select('review_count, rating')
      .eq('url', appUrl)
      .single();

    if (competitorError) {
      console.error('Error fetching competitor review count and rating from competitor_shopify_apps:', competitorError);
      // Continue even if competitor fetch fails
    }

    // If both fetches failed or found nothing, return null
    if (!currentAppData && !competitorAppData) {
      console.warn('No review data found in either table for url:', appUrl);
      return null;
    }

    return {
      currentReviewCount: currentAppData?.review_count ?? null,
      competitorReviewCount: competitorAppData?.review_count ?? null,
      currentRating: currentAppData?.rating ?? null,
      competitorRating: competitorAppData?.rating ?? null,
    };

  } catch (error) {
    console.error('Unexpected error in fetchReviewCountsAndRatingsForComparison:', error);
    return null;
  }
}

export async function fetchMarketLeadersByCategoryFeature(categoryFeatureHeading: string = 'SEO'): Promise<(App & { categories: string[]; marketShare?: number })[] | null> {
  // First get all apps with this category feature
  const apps = await fetchAppsByCategoryFeature(categoryFeatureHeading);
  
  if (!apps || apps.length === 0) {
    return null;
  }

  // Calculate total reviews for market share calculation
  const totalReviews = apps.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0);

  const appsWithLeadershipScores = apps.map(app => {
    const appReviewCount = Number(app.review_count) || 0;
    const appRating = app.rating || 0;

    // 1. Basic Market Share (Review-Based)
    const reviewMarketShare = totalReviews > 0 ? (appReviewCount / totalReviews) * 100 : 0;

    // 2. Quality Score (0-1 scale)
    // Simplified as individual star counts are not available.
    const qualityScore = (appRating - 1) / 4; // Converts 1-5 rating to 0-1 scale

    // 3. Market Leader Score (Simple Leadership Formula)
    // Review_Market_Share normalized to 0-1 (divide by 100)
    const leadershipScore = (reviewMarketShare / 100 * 0.6) + (qualityScore * 0.4);

    return {
      ...app,
      marketShare: reviewMarketShare,
      qualityScore: qualityScore,
      leadershipScore: leadershipScore,
    };
  });

  // Sort apps by Leadership Score
  const sortedApps = appsWithLeadershipScores
    .sort((a, b) => (b.leadershipScore || 0) - (a.leadershipScore || 0))
    // .slice(0, 10); // Display only top 10 apps

  // 4. Leader Classification (Optional: can be done in the frontend if needed for display)
  // This classification logic can be added here or in the component that renders the leaders.
  // For now, I'm calculating the scores and sorting.

  return sortedApps;
}

